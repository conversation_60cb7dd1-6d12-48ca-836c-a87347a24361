// Akış <PERSON>ı<PERSON>ı<PERSON>ı - Mikro-Evren

class CosmicStructure {
    constructor(x, y, type, level = 1) {
        this.x = x;
        this.y = y;
        this.type = type; // 'sphere', 'ring', 'spiral', 'generator'
        this.level = level;
        this.size = 20 + level * 10;
        this.rotation = 0;
        this.pulsePhase = Math.random() * Math.PI * 2;
        this.energy = 0;
        this.maxEnergy = 100 * level;
        this.isActive = true;
        this.particles = [];
        this.color = this.getColorByType();
        this.productionRate = level * 0.1; // Enerji üretim hızı
    }

    getColorByType() {
        const colors = {
            'sphere': '#64ffda',
            'ring': '#00bcd4',
            'spiral': '#9c27b0',
            'generator': '#ff6440'
        };
        return colors[this.type] || '#64ffda';
    }

    update(deltaTime) {
        this.rotation += deltaTime * 0.5;
        this.pulsePhase += deltaTime * 2;
        
        // Enerji üretimi
        if (this.isActive) {
            this.energy = Math.min(this.maxEnergy, this.energy + this.productionRate * deltaTime);
        }
        
        // Tip-özel güncellemeler
        switch (this.type) {
            case 'generator':
                this.updateGenerator(deltaTime);
                break;
            case 'spiral':
                this.updateSpiral(deltaTime);
                break;
        }
        
        // Partikül güncelleme
        this.particles.forEach(particle => {
            particle.update(deltaTime, 800, 600);
        });
        
        // Ölü partikülleri temizle
        this.particles = this.particles.filter(p => p.life > 0);
    }

    updateGenerator(deltaTime) {
        // Periyodik partikül üretimi
        if (Math.random() < this.productionRate * deltaTime) {
            this.createParticle();
        }
    }

    updateSpiral(deltaTime) {
        // Spiral hareket efekti
        const spiralRadius = this.size * 0.5;
        const angle = this.rotation * 2;
        
        if (Math.random() < 0.1 * deltaTime) {
            const particle = new Particle(
                this.x + Math.cos(angle) * spiralRadius,
                this.y + Math.sin(angle) * spiralRadius,
                this.color,
                2
            );
            particle.vx = Math.cos(angle + Math.PI/2) * 0.5;
            particle.vy = Math.sin(angle + Math.PI/2) * 0.5;
            this.particles.push(particle);
        }
    }

    createParticle() {
        const angle = Math.random() * Math.PI * 2;
        const distance = this.size * 0.5;
        const particle = new Particle(
            this.x + Math.cos(angle) * distance,
            this.y + Math.sin(angle) * distance,
            this.color,
            1 + Math.random() * 2
        );
        particle.vx = Math.cos(angle) * (0.5 + Math.random() * 0.5);
        particle.vy = Math.sin(angle) * (0.5 + Math.random() * 0.5);
        particle.life = 0.5 + Math.random() * 0.5;
        this.particles.push(particle);
    }

    draw(ctx) {
        ctx.save();
        
        // Ana yapı
        const pulseSize = this.size + Math.sin(this.pulsePhase) * 3;
        
        switch (this.type) {
            case 'sphere':
                this.drawSphere(ctx, pulseSize);
                break;
            case 'ring':
                this.drawRing(ctx, pulseSize);
                break;
            case 'spiral':
                this.drawSpiral(ctx, pulseSize);
                break;
            case 'generator':
                this.drawGenerator(ctx, pulseSize);
                break;
        }
        
        // Enerji göstergesi
        this.drawEnergyIndicator(ctx);
        
        // Partikülleri çiz
        this.particles.forEach(particle => {
            particle.draw(ctx);
        });
        
        ctx.restore();
    }

    drawSphere(ctx, size) {
        // Dış parıltı
        const gradient = ctx.createRadialGradient(
            this.x, this.y, 0,
            this.x, this.y, size
        );
        gradient.addColorStop(0, this.color + '80');
        gradient.addColorStop(1, this.color + '00');
        
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(this.x, this.y, size, 0, Math.PI * 2);
        ctx.fill();
        
        // Ana küre
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(this.x, this.y, size * 0.6, 0, Math.PI * 2);
        ctx.fill();
    }

    drawRing(ctx, size) {
        ctx.strokeStyle = this.color;
        ctx.lineWidth = 4;
        ctx.shadowColor = this.color;
        ctx.shadowBlur = 10;
        
        // Dış halka
        ctx.beginPath();
        ctx.arc(this.x, this.y, size, 0, Math.PI * 2);
        ctx.stroke();
        
        // İç halka
        ctx.beginPath();
        ctx.arc(this.x, this.y, size * 0.6, 0, Math.PI * 2);
        ctx.stroke();
        
        // Dönen noktalar
        for (let i = 0; i < 8; i++) {
            const angle = this.rotation + (i * Math.PI * 2) / 8;
            const x = this.x + Math.cos(angle) * size * 0.8;
            const y = this.y + Math.sin(angle) * size * 0.8;
            
            ctx.fillStyle = this.color;
            ctx.beginPath();
            ctx.arc(x, y, 3, 0, Math.PI * 2);
            ctx.fill();
        }
    }

    drawSpiral(ctx, size) {
        ctx.strokeStyle = this.color;
        ctx.lineWidth = 3;
        ctx.shadowColor = this.color;
        ctx.shadowBlur = 8;
        
        ctx.beginPath();
        let radius = 0;
        const maxRadius = size;
        const turns = 3;
        
        for (let angle = 0; angle < turns * Math.PI * 2; angle += 0.1) {
            radius = (angle / (turns * Math.PI * 2)) * maxRadius;
            const x = this.x + Math.cos(angle + this.rotation) * radius;
            const y = this.y + Math.sin(angle + this.rotation) * radius;
            
            if (angle === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        }
        ctx.stroke();
    }

    drawGenerator(ctx, size) {
        // Merkez çekirdek
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(this.x, this.y, size * 0.3, 0, Math.PI * 2);
        ctx.fill();
        
        // Dönen çubuklar
        for (let i = 0; i < 6; i++) {
            const angle = this.rotation + (i * Math.PI * 2) / 6;
            const startX = this.x + Math.cos(angle) * size * 0.4;
            const startY = this.y + Math.sin(angle) * size * 0.4;
            const endX = this.x + Math.cos(angle) * size;
            const endY = this.y + Math.sin(angle) * size;
            
            ctx.strokeStyle = this.color;
            ctx.lineWidth = 3;
            ctx.shadowColor = this.color;
            ctx.shadowBlur = 5;
            
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.stroke();
        }
    }

    drawEnergyIndicator(ctx) {
        const barWidth = this.size * 1.5;
        const barHeight = 4;
        const barX = this.x - barWidth / 2;
        const barY = this.y + this.size + 10;
        
        // Arka plan
        ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
        ctx.fillRect(barX, barY, barWidth, barHeight);
        
        // Enerji çubuğu
        const energyWidth = (this.energy / this.maxEnergy) * barWidth;
        ctx.fillStyle = this.color;
        ctx.fillRect(barX, barY, energyWidth, barHeight);
    }

    // Enerji toplama
    harvestEnergy() {
        const harvested = this.energy;
        this.energy = 0;
        return harvested;
    }

    // Seviye yükseltme
    upgrade() {
        this.level++;
        this.size = 20 + this.level * 10;
        this.maxEnergy = 100 * this.level;
        this.productionRate = this.level * 0.1;
    }
}

class MicroUniverse {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.structures = [];
        this.totalEnergy = 0;
        this.backgroundParticles = [];
        this.selectedStructure = null;
        this.camera = { x: 0, y: 0, zoom: 1 };
        this.shopItems = this.initializeShop();
        this.initializeBackground();
    }

    initializeShop() {
        return [
            {
                id: 'sphere',
                name: 'Enerji Küresi',
                description: 'Temel enerji üretici yapı',
                cost: 100,
                type: 'sphere',
                unlocked: true
            },
            {
                id: 'ring',
                name: 'Kozmik Halka',
                description: 'Gelişmiş enerji manipülatörü',
                cost: 250,
                type: 'ring',
                unlocked: false
            },
            {
                id: 'spiral',
                name: 'Spiral Jeneratör',
                description: 'Sürekli partikül üreticisi',
                cost: 500,
                type: 'spiral',
                unlocked: false
            },
            {
                id: 'generator',
                name: 'Mega Jeneratör',
                description: 'Yüksek kapasiteli enerji üreticisi',
                cost: 1000,
                type: 'generator',
                unlocked: false
            }
        ];
    }

    initializeBackground() {
        // Arka plan partikülleri
        for (let i = 0; i < 50; i++) {
            this.backgroundParticles.push({
                x: Math.random() * this.canvas.width,
                y: Math.random() * this.canvas.height,
                size: Math.random() * 2 + 0.5,
                speed: Math.random() * 0.5 + 0.1,
                angle: Math.random() * Math.PI * 2,
                opacity: Math.random() * 0.5 + 0.1,
                color: '#64ffda'
            });
        }
    }

    addStructure(type, x, y) {
        const structure = new CosmicStructure(x, y, type);
        this.structures.push(structure);
        return structure;
    }

    removeStructure(structure) {
        const index = this.structures.indexOf(structure);
        if (index > -1) {
            this.structures.splice(index, 1);
        }
    }

    update(deltaTime) {
        // Arka plan partikülleri
        this.backgroundParticles.forEach(particle => {
            particle.x += Math.cos(particle.angle) * particle.speed;
            particle.y += Math.sin(particle.angle) * particle.speed;
            
            // Ekran sınırları
            if (particle.x < 0) particle.x = this.canvas.width;
            if (particle.x > this.canvas.width) particle.x = 0;
            if (particle.y < 0) particle.y = this.canvas.height;
            if (particle.y > this.canvas.height) particle.y = 0;
        });
        
        // Yapıları güncelle
        this.structures.forEach(structure => {
            structure.update(deltaTime);
        });
        
        // Otomatik enerji toplama
        this.autoHarvestEnergy();
    }

    autoHarvestEnergy() {
        this.structures.forEach(structure => {
            if (structure.energy >= structure.maxEnergy * 0.8) {
                const harvested = structure.harvestEnergy();
                this.totalEnergy += harvested;
            }
        });
    }

    draw() {
        // Canvas temizleme
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Arka plan gradyanı
        const gradient = this.ctx.createRadialGradient(
            this.canvas.width / 2, this.canvas.height / 2, 0,
            this.canvas.width / 2, this.canvas.height / 2, Math.max(this.canvas.width, this.canvas.height)
        );
        gradient.addColorStop(0, '#1a1a2e');
        gradient.addColorStop(1, '#0c0c0c');
        
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Arka plan partikülleri
        this.backgroundParticles.forEach(particle => {
            this.ctx.save();
            this.ctx.globalAlpha = particle.opacity;
            this.ctx.fillStyle = particle.color;
            this.ctx.beginPath();
            this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            this.ctx.fill();
            this.ctx.restore();
        });
        
        // Yapıları çiz
        this.structures.forEach(structure => {
            structure.draw(this.ctx);
            
            // Seçili yapı vurgusu
            if (structure === this.selectedStructure) {
                this.drawSelectionHighlight(structure);
            }
        });
        
        // Bağlantı çizgileri
        this.drawConnections();
    }

    drawSelectionHighlight(structure) {
        this.ctx.save();
        this.ctx.strokeStyle = '#64ffda';
        this.ctx.lineWidth = 2;
        this.ctx.setLineDash([5, 5]);
        this.ctx.beginPath();
        this.ctx.arc(structure.x, structure.y, structure.size + 10, 0, Math.PI * 2);
        this.ctx.stroke();
        this.ctx.restore();
    }

    drawConnections() {
        // Yakın yapılar arasında enerji bağlantıları çiz
        for (let i = 0; i < this.structures.length; i++) {
            for (let j = i + 1; j < this.structures.length; j++) {
                const struct1 = this.structures[i];
                const struct2 = this.structures[j];
                const distance = Math.sqrt(
                    (struct1.x - struct2.x) ** 2 + (struct1.y - struct2.y) ** 2
                );
                
                if (distance < 150) {
                    this.ctx.save();
                    this.ctx.strokeStyle = '#64ffda20';
                    this.ctx.lineWidth = 1;
                    this.ctx.beginPath();
                    this.ctx.moveTo(struct1.x, struct1.y);
                    this.ctx.lineTo(struct2.x, struct2.y);
                    this.ctx.stroke();
                    this.ctx.restore();
                }
            }
        }
    }

    // Tıklama işleme
    handleClick(x, y) {
        // Yapı seçimi
        for (let structure of this.structures) {
            const distance = Math.sqrt((structure.x - x) ** 2 + (structure.y - y) ** 2);
            if (distance < structure.size) {
                this.selectedStructure = structure;
                return structure;
            }
        }
        
        this.selectedStructure = null;
        return null;
    }

    // Mağaza öğesi satın alma
    purchaseItem(itemId) {
        const item = this.shopItems.find(i => i.id === itemId);
        if (!item || !item.unlocked || this.totalEnergy < item.cost) {
            return false;
        }
        
        this.totalEnergy -= item.cost;
        
        // Rastgele konumda yapı oluştur
        const x = 100 + Math.random() * (this.canvas.width - 200);
        const y = 100 + Math.random() * (this.canvas.height - 200);
        
        this.addStructure(item.type, x, y);
        return true;
    }

    // Yapı yükseltme
    upgradeStructure(structure) {
        const cost = structure.level * 100;
        if (this.totalEnergy >= cost) {
            this.totalEnergy -= cost;
            structure.upgrade();
            return true;
        }
        return false;
    }

    // Mağaza kilidini açma
    unlockShopItems() {
        const structureCount = this.structures.length;
        
        if (structureCount >= 2) {
            this.shopItems[1].unlocked = true; // Ring
        }
        if (structureCount >= 5) {
            this.shopItems[2].unlocked = true; // Spiral
        }
        if (structureCount >= 10) {
            this.shopItems[3].unlocked = true; // Generator
        }
    }

    // İstatistikler
    getStats() {
        const totalProduction = this.structures.reduce((sum, s) => sum + s.productionRate, 0);
        const totalCapacity = this.structures.reduce((sum, s) => sum + s.maxEnergy, 0);
        
        return {
            totalEnergy: Math.floor(this.totalEnergy),
            structureCount: this.structures.length,
            totalProduction: totalProduction.toFixed(1),
            totalCapacity: Math.floor(totalCapacity),
            selectedStructure: this.selectedStructure
        };
    }

    // Kaydetme/Yükleme
    saveUniverse() {
        const data = {
            totalEnergy: this.totalEnergy,
            structures: this.structures.map(s => ({
                x: s.x,
                y: s.y,
                type: s.type,
                level: s.level,
                energy: s.energy
            })),
            shopItems: this.shopItems
        };
        localStorage.setItem('microUniverse', JSON.stringify(data));
    }

    loadUniverse() {
        const saved = localStorage.getItem('microUniverse');
        if (saved) {
            const data = JSON.parse(saved);
            this.totalEnergy = data.totalEnergy || 0;
            this.shopItems = data.shopItems || this.shopItems;
            
            // Yapıları yeniden oluştur
            this.structures = [];
            if (data.structures) {
                data.structures.forEach(structData => {
                    const structure = new CosmicStructure(
                        structData.x, structData.y, structData.type, structData.level
                    );
                    structure.energy = structData.energy || 0;
                    this.structures.push(structure);
                });
            }
        }
    }
}
