/* <PERSON><PERSON><PERSON><PERSON><PERSON> - <PERSON> */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
    color: #ffffff;
    overflow: hidden;
    height: 100vh;
}

#game-container {
    width: 100vw;
    height: 100vh;
    position: relative;
}

/* Ekran Geçişleri */
.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
    flex-direction: column;
    transition: opacity 0.5s ease-in-out;
}

.screen.active {
    display: flex;
}

/* <PERSON> */
#main-game {
    justify-content: space-between;
}

#game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

#level-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

#level-number {
    font-size: 1.2em;
    font-weight: bold;
    color: #64ffda;
}

#objective {
    font-size: 0.9em;
    color: #b0bec5;
}

#energy-counter {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(100, 255, 218, 0.1);
    padding: 10px 15px;
    border-radius: 25px;
    border: 1px solid rgba(100, 255, 218, 0.3);
}

#energy-icon {
    font-size: 1.5em;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

#energy-amount {
    font-size: 1.1em;
    font-weight: bold;
    color: #64ffda;
}

/* Canvas Alanı */
#game-canvas {
    flex: 1;
    background: radial-gradient(circle at center, #1a1a2e 0%, #0c0c0c 100%);
    border: 2px solid rgba(100, 255, 218, 0.2);
    margin: 0 20px;
    border-radius: 10px;
    cursor: crosshair;
    box-shadow:
        inset 0 0 50px rgba(100, 255, 218, 0.1),
        0 0 30px rgba(100, 255, 218, 0.2);
}

/* Oyun Kontrolleri */
#game-controls {
    display: flex;
    justify-content: center;
    gap: 20px;
    padding: 20px;
}

#game-controls button {
    background: rgba(100, 255, 218, 0.1);
    border: 2px solid rgba(100, 255, 218, 0.3);
    color: #64ffda;
    padding: 15px 20px;
    border-radius: 50%;
    font-size: 1.2em;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

#game-controls button:hover {
    background: rgba(100, 255, 218, 0.2);
    border-color: rgba(100, 255, 218, 0.5);
    transform: scale(1.1);
    box-shadow: 0 0 20px rgba(100, 255, 218, 0.4);
}

/* İlerleme Çubuğu */
#progress-bar {
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    margin: 0 20px 20px 20px;
    border-radius: 3px;
    overflow: hidden;
}

#progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #64ffda, #00bcd4);
    width: 0%;
    transition: width 0.3s ease;
    box-shadow: 0 0 10px rgba(100, 255, 218, 0.5);
}

/* Başlangıç Ekranı */
#start-screen {
    justify-content: center;
    align-items: center;
    text-align: center;
}

#title h1 {
    font-size: 3em;
    margin-bottom: 20px;
    background: linear-gradient(45deg, #64ffda, #00bcd4, #3f51b5);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: glow 3s ease-in-out infinite alternate;
}

@keyframes glow {
    from { filter: drop-shadow(0 0 20px rgba(100, 255, 218, 0.5)); }
    to { filter: drop-shadow(0 0 30px rgba(100, 255, 218, 0.8)); }
}

#title p {
    font-size: 1.2em;
    color: #b0bec5;
    margin-bottom: 40px;
}

#start-screen button, #level-complete button {
    background: linear-gradient(45deg, #64ffda, #00bcd4);
    border: none;
    color: #0c0c0c;
    padding: 15px 30px;
    margin: 10px;
    border-radius: 25px;
    font-size: 1.1em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

#start-screen button:hover, #level-complete button:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(100, 255, 218, 0.4);
}

/* Mikro-Evren Ekranı */
#micro-universe {
    padding: 20px;
}

#universe-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

#universe-header h2 {
    color: #64ffda;
    font-size: 2em;
}

#back-to-game {
    background: rgba(100, 255, 218, 0.1);
    border: 2px solid rgba(100, 255, 218, 0.3);
    color: #64ffda;
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

#universe-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: 30px;
}

.stat {
    text-align: center;
    padding: 15px;
    background: rgba(100, 255, 218, 0.1);
    border-radius: 10px;
    border: 1px solid rgba(100, 255, 218, 0.2);
}

.stat-label {
    display: block;
    color: #b0bec5;
    font-size: 0.9em;
    margin-bottom: 5px;
}

/* Ses Kontrolleri */
#audio-controls {
    position: fixed;
    top: 20px;
    right: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(0, 0, 0, 0.5);
    padding: 10px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

#sound-toggle {
    background: none;
    border: none;
    font-size: 1.2em;
    cursor: pointer;
    color: #64ffda;
}

#volume-slider {
    width: 80px;
    accent-color: #64ffda;
}

/* Responsive Tasarım */
@media (max-width: 768px) {
    #game-header {
        padding: 15px;
    }

    #title h1 {
        font-size: 2em;
    }

    #game-controls {
        gap: 15px;
    }

    #game-controls button {
        padding: 12px 15px;
        font-size: 1em;
    }
}

/* Mağaza Stilleri */
#universe-shop {
    margin-top: 30px;
}

#universe-shop h3 {
    color: #64ffda;
    margin-bottom: 20px;
    text-align: center;
}

#shop-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    max-height: 300px;
    overflow-y: auto;
}

.shop-item {
    background: rgba(100, 255, 218, 0.1);
    border: 2px solid rgba(100, 255, 218, 0.3);
    border-radius: 10px;
    padding: 15px;
    transition: all 0.3s ease;
}

.shop-item.unlocked {
    border-color: rgba(100, 255, 218, 0.5);
}

.shop-item.locked {
    opacity: 0.5;
    border-color: rgba(255, 255, 255, 0.2);
}

.shop-item h4 {
    color: #64ffda;
    margin-bottom: 10px;
    font-size: 1.1em;
}

.shop-item p {
    color: #b0bec5;
    font-size: 0.9em;
    margin-bottom: 15px;
    line-height: 1.4;
}

.shop-item .cost {
    color: #ffeb3b;
    font-weight: bold;
    margin-bottom: 10px;
}

.shop-item button {
    width: 100%;
    background: linear-gradient(45deg, #64ffda, #00bcd4);
    border: none;
    color: #0c0c0c;
    padding: 10px;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.shop-item button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(100, 255, 218, 0.4);
}

.shop-item button:disabled {
    background: rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.5);
    cursor: not-allowed;
}

/* Seviye Tamamlama Ekranı */
#level-complete {
    justify-content: center;
    align-items: center;
    text-align: center;
}

#completion-content {
    background: rgba(0, 0, 0, 0.8);
    padding: 40px;
    border-radius: 20px;
    border: 2px solid rgba(100, 255, 218, 0.3);
    backdrop-filter: blur(10px);
}

#completion-content h2 {
    color: #64ffda;
    font-size: 2.5em;
    margin-bottom: 30px;
    text-shadow: 0 0 20px rgba(100, 255, 218, 0.5);
}

#completion-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: 30px;
    gap: 20px;
}

.completion-stat {
    text-align: center;
    padding: 15px;
    background: rgba(100, 255, 218, 0.1);
    border-radius: 10px;
    border: 1px solid rgba(100, 255, 218, 0.2);
    min-width: 120px;
}

.completion-stat .stat-label {
    display: block;
    color: #b0bec5;
    font-size: 0.9em;
    margin-bottom: 5px;
}

.completion-stat .stat-value {
    color: #64ffda;
    font-size: 1.5em;
    font-weight: bold;
}

#completion-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.completion-stars {
    font-size: 2em;
    color: #ffeb3b;
    margin: 20px 0;
    text-shadow: 0 0 10px rgba(255, 235, 59, 0.5);
}

/* Animasyonlar */
.particle-trail {
    position: absolute;
    pointer-events: none;
    border-radius: 50%;
    animation: trail-fade 0.5s ease-out forwards;
}

@keyframes trail-fade {
    from {
        opacity: 0.8;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.5);
    }
}

/* Kombo animasyonu */
@keyframes combo-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.combo-indicator {
    animation: combo-pulse 0.5s ease-in-out;
}
