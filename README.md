# Akış Yaratıcısı (Flow Creator)

Kozmik partikülleri yönlendir, ak<PERSON>şlar yarat, evreni şekillendir!

## 🌟 Oyun Hakkında

**Akış Yaratıcısı**, ben<PERSON><PERSON> bir partikül manipülasyon oyunudur. Oyuncular, dokunma ve sürükleme hareketleriyle kozmik partikülleri yönlendirir, akış alanları oluşturur ve çeşitli deneyleri tamamlar. Her başarılı deney, oyuncunun kişisel "Mikro-Evrenini" genişletir.

### ✨ Temel Özellikler

- **Sezgisel Kontroller**: Tek dokunuş ve sürükleme ile partikül akışlarını kontrol edin
- **Görsel Şölen**: Parlayan partiküller, akış efektleri ve büyüleyici animasyonlar
- **<PERSON>k<PERSON>ş <PERSON>mboları**: Art arda başarıl<PERSON> hareketlerle ekstra ödüller kazanın
- **Mikro-Evren**: Kazandığınız enerjiyle kendi kozmik yapılarınızı inşa edin
- **İlerleme Sistemi**: 6 farklı deney türü ve artan zorluk seviyeleri

## 🎮 Nasıl Oynanır

### Temel Kontroller
- **Tek Dokunuş**: Manyetik alan oluşturur (partikülleri çeker)
- **Sürükleme**: Akış yolu çizer (partiküller yolu takip eder)
- **Uzun Basma**: İtici alan oluşturur (partikülleri iter)

### Oyun Hedefleri
1. **Partikül Toplama**: Belirli sayıda partiküli hedef alana yönlendirin
2. **Renk Dönüşümü**: Partiküllerin rengini değiştirin
3. **Yoğunluk Kontrolü**: Belirli alanda partikül yoğunluğu oluşturun
4. **Akış Komboları**: Art arda başarılı hareketler yapın
5. **Hız Kontrolü**: Partikülleri belirli hızlara çıkarın
6. **Karmaşık Hedefler**: Çoklu görevleri aynı anda tamamlayın

### Akış Komboları
- Hızlı ve hassas hareketlerle kombo zinciri oluşturun
- Her kombo, enerji kazancınızı artırır
- Kombo çarpanı maksimum 5x'e kadar çıkabilir

## 🌌 Mikro-Evren

Kazandığınız enerjiyle kendi kozmik yapılarınızı inşa edin:

### Yapı Türleri
- **Enerji Küresi**: Temel enerji üretici
- **Kozmik Halka**: Gelişmiş enerji manipülatörü  
- **Spiral Jeneratör**: Sürekli partikül üreticisi
- **Mega Jeneratör**: Yüksek kapasiteli enerji üreticisi

### Özellikler
- Otomatik enerji üretimi
- Yapı yükseltme sistemi
- Görsel partikül efektleri
- Yapılar arası enerji bağlantıları

## 🛠️ Teknik Detaylar

### Teknolojiler
- **HTML5 Canvas**: Yüksek performanslı grafik rendering
- **JavaScript ES6+**: Modern oyun mekaniği
- **CSS3**: Responsive tasarım ve animasyonlar
- **LocalStorage**: İlerleme kaydetme

### Dosya Yapısı
```
akis-yaraticisi/
├── index.html              # Ana HTML dosyası
├── css/
│   └── style.css           # Stil dosyası
├── js/
│   ├── main.js             # Ana oyun döngüsü
│   ├── particle-system.js  # Partikül sistemi
│   ├── flow-mechanics.js   # Akış mekaniği
│   ├── level-manager.js    # Seviye yönetimi
│   └── micro-universe.js   # Mikro-evren sistemi
└── README.md
```

### Performans Optimizasyonları
- Efficient particle pooling
- Canvas optimizasyonları
- Delta time based animations
- Memory management

## 🚀 Kurulum ve Çalıştırma

1. Projeyi klonlayın veya indirin
2. Bir web sunucusu başlatın (örn: Live Server)
3. `index.html` dosyasını açın
4. Oyunun keyfini çıkarın!

### Geliştirme Ortamı
```bash
# Basit HTTP sunucusu (Python)
python -m http.server 8000

# Node.js ile
npx serve .

# VS Code Live Server eklentisi önerilir
```

## 🎯 Gelecek Özellikler

- [ ] Ses efektleri ve ambient müzik
- [ ] Daha fazla partikül türü
- [ ] Çoklu oyuncu modu
- [ ] Başarım sistemi
- [ ] Özel seviye editörü
- [ ] Mobil uygulama versiyonu

## 🤝 Katkıda Bulunma

Bu proje açık kaynak kodludur. Katkılarınızı bekliyoruz!

1. Fork yapın
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Değişikliklerinizi commit edin (`git commit -m 'Add amazing feature'`)
4. Branch'inizi push edin (`git push origin feature/amazing-feature`)
5. Pull Request oluşturun

## 📝 Lisans

Bu proje MIT lisansı altında lisanslanmıştır. Detaylar için `LICENSE` dosyasına bakın.

## 🎨 Tasarım Felsefesi

**Akış Yaratıcısı**, oyuncuya meditatif ve yaratıcı bir deneyim sunmayı hedefler. Karmaşık fizik simülasyonları yerine, sezgisel ve tatmin edici etkileşimlere odaklanır. Her hareket anında görsel geri bildirim sağlar ve oyuncuyu "akış" (flow) durumuna sokar.

### Renk Paleti
- **Ana Renk**: Cyan (#64ffda) - Enerji ve teknoloji
- **İkincil**: Mavi tonları - Kozmik atmosfer
- **Vurgu**: Mor ve pembe - Mistik elementler
- **Arka Plan**: Koyu tonlar - Uzay boşluğu

---

**Geliştirici**: Augment Agent  
**Versiyon**: 1.0.0  
**Son Güncelleme**: 2024

*Kozmik akışların gücünü keşfedin!* ✨
