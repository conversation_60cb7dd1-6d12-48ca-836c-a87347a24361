// Ak<PERSON>ş <PERSON>ı - Seviye Yöneticisi

class Level {
    constructor(id, name, objective, targetConditions, timeLimit = null) {
        this.id = id;
        this.name = name;
        this.objective = objective;
        this.targetConditions = targetConditions;
        this.timeLimit = timeLimit;
        this.startTime = null;
        this.completed = false;
        this.energyReward = 100;
        this.bonusReward = 0;
        this.stars = 0; // 1-3 yıldız sistemi
    }

    start() {
        this.startTime = Date.now();
        this.completed = false;
    }

    checkCompletion(particleSystem, flowMechanics) {
        if (this.completed) return true;

        let conditionsMet = 0;
        const totalConditions = this.targetConditions.length;

        this.targetConditions.forEach(condition => {
            if (this.evaluateCondition(condition, particleSystem, flowMechanics)) {
                conditionsMet++;
            }
        });

        if (conditionsMet === totalConditions) {
            this.completed = true;
            this.calculateReward(flowMechanics);
            return true;
        }

        return false;
    }

    evaluateCondition(condition, particleSystem, flowMechanics) {
        switch (condition.type) {
            case 'collect_particles':
                return this.checkCollectParticles(condition, particleSystem);
            case 'color_transform':
                return this.checkColorTransform(condition, particleSystem);
            case 'area_density':
                return this.checkAreaDensity(condition, particleSystem);
            case 'combo_count':
                return this.checkComboCount(condition, flowMechanics);
            case 'time_limit':
                return this.checkTimeLimit(condition);
            case 'particle_speed':
                return this.checkParticleSpeed(condition, particleSystem);
            default:
                return false;
        }
    }

    checkCollectParticles(condition, particleSystem) {
        const targetArea = condition.targetArea;
        const requiredCount = condition.count;
        const requiredColor = condition.color;

        const particlesInArea = particleSystem.getParticlesInArea(
            targetArea.x, targetArea.y, targetArea.radius
        );

        if (requiredColor) {
            const coloredParticles = particlesInArea.filter(p => p.color === requiredColor);
            return coloredParticles.length >= requiredCount;
        }

        return particlesInArea.length >= requiredCount;
    }

    checkColorTransform(condition, particleSystem) {
        const requiredColor = condition.toColor;
        const coloredParticles = particleSystem.getParticlesByColor(requiredColor);
        return coloredParticles.length >= condition.count;
    }

    checkAreaDensity(condition, particleSystem) {
        const area = condition.area;
        const requiredDensity = condition.density;
        const particlesInArea = particleSystem.getParticlesInArea(
            area.x, area.y, area.radius
        );
        const currentDensity = particlesInArea.length / (Math.PI * area.radius * area.radius);
        return currentDensity >= requiredDensity;
    }

    checkComboCount(condition, flowMechanics) {
        const stats = flowMechanics.getStats();
        return stats.totalCombos >= condition.count;
    }

    checkTimeLimit(condition) {
        if (!this.startTime) return false;
        const elapsed = (Date.now() - this.startTime) / 1000;
        return elapsed <= condition.timeLimit;
    }

    checkParticleSpeed(condition, particleSystem) {
        const fastParticles = particleSystem.particles.filter(particle => {
            const speed = Math.sqrt(particle.vx * particle.vx + particle.vy * particle.vy);
            return speed >= condition.minSpeed;
        });
        return fastParticles.length >= condition.count;
    }

    calculateReward(flowMechanics) {
        const stats = flowMechanics.getStats();
        const completionTime = this.startTime ? (Date.now() - this.startTime) / 1000 : 0;

        // Temel ödül
        let totalReward = this.energyReward;

        // Kombo bonusu
        totalReward += stats.totalCombos * 10;

        // Hız bonusu
        if (this.timeLimit && completionTime < this.timeLimit * 0.5) {
            totalReward *= 2;
            this.stars = 3;
        } else if (this.timeLimit && completionTime < this.timeLimit * 0.75) {
            totalReward *= 1.5;
            this.stars = 2;
        } else {
            this.stars = 1;
        }

        this.bonusReward = totalReward - this.energyReward;
        this.energyReward = totalReward;
    }

    getProgress(particleSystem, flowMechanics) {
        let totalProgress = 0;
        const conditionCount = this.targetConditions.length;

        this.targetConditions.forEach(condition => {
            totalProgress += this.getConditionProgress(condition, particleSystem, flowMechanics);
        });

        return Math.min(1.0, totalProgress / conditionCount);
    }

    getConditionProgress(condition, particleSystem, flowMechanics) {
        switch (condition.type) {
            case 'collect_particles':
                const targetArea = condition.targetArea;
                const particlesInArea = particleSystem.getParticlesInArea(
                    targetArea.x, targetArea.y, targetArea.radius
                );
                return Math.min(1.0, particlesInArea.length / condition.count);

            case 'color_transform':
                const coloredParticles = particleSystem.getParticlesByColor(condition.toColor);
                return Math.min(1.0, coloredParticles.length / condition.count);

            case 'combo_count':
                const stats = flowMechanics.getStats();
                return Math.min(1.0, stats.totalCombos / condition.count);

            default:
                return this.evaluateCondition(condition, particleSystem, flowMechanics) ? 1.0 : 0.0;
        }
    }
}

class LevelManager {
    constructor() {
        this.levels = [];
        this.currentLevelIndex = 0;
        this.currentLevel = null;
        this.totalEnergy = 0;
        this.completedLevels = [];
        this.initializeLevels();
    }

    initializeLevels() {
        // Seviye 1: Temel Toplama
        this.levels.push(new Level(
            1,
            "İlk Deney",
            "Tüm partikülleri merkez alanına topla",
            [{
                type: 'collect_particles',
                count: 10,
                targetArea: { x: 400, y: 300, radius: 80 }
            }],
            60
        ));

        // Seviye 2: Renk Dönüşümü
        this.levels.push(new Level(
            2,
            "Renk Uyumu",
            "5 partiküli mavi renge dönüştür",
            [{
                type: 'color_transform',
                toColor: '#00bcd4',
                count: 5
            }],
            45
        ));

        // Seviye 3: Yoğunluk Kontrolü
        this.levels.push(new Level(
            3,
            "Enerji Yoğunluğu",
            "Belirli alanda yüksek partikül yoğunluğu oluştur",
            [{
                type: 'area_density',
                area: { x: 200, y: 200, radius: 60 },
                density: 0.001
            }],
            90
        ));

        // Seviye 4: Kombo Ustası
        this.levels.push(new Level(
            4,
            "Akış Komboları",
            "5 akış kombosu gerçekleştir",
            [{
                type: 'combo_count',
                count: 5
            }],
            120
        ));

        // Seviye 5: Hız Kontrolü
        this.levels.push(new Level(
            5,
            "Hızlı Akış",
            "10 partiküli yüksek hıza çıkar",
            [{
                type: 'particle_speed',
                minSpeed: 3.0,
                count: 10
            }],
            75
        ));

        // Seviye 6: Karmaşık Hedef
        this.levels.push(new Level(
            6,
            "Usta Deneyi",
            "Çoklu hedefleri tamamla",
            [
                {
                    type: 'collect_particles',
                    count: 8,
                    color: '#64ffda',
                    targetArea: { x: 150, y: 150, radius: 50 }
                },
                {
                    type: 'color_transform',
                    toColor: '#9c27b0',
                    count: 6
                },
                {
                    type: 'combo_count',
                    count: 3
                }
            ],
            180
        ));
    }

    getCurrentLevel() {
        return this.currentLevel;
    }

    startLevel(levelIndex = null) {
        if (levelIndex !== null) {
            this.currentLevelIndex = levelIndex;
        }

        if (this.currentLevelIndex < this.levels.length) {
            this.currentLevel = this.levels[this.currentLevelIndex];
            this.currentLevel.start();
            return this.currentLevel;
        }

        return null;
    }

    checkLevelCompletion(particleSystem, flowMechanics) {
        if (!this.currentLevel) return false;

        if (this.currentLevel.checkCompletion(particleSystem, flowMechanics)) {
            this.completeLevel();
            return true;
        }

        return false;
    }

    completeLevel() {
        if (!this.currentLevel) return;

        // Enerji ödülü ekle
        this.totalEnergy += this.currentLevel.energyReward;

        // Tamamlanan seviyeyi kaydet
        if (!this.completedLevels.includes(this.currentLevel.id)) {
            this.completedLevels.push(this.currentLevel.id);
        }

        // İlerlemeyi kaydet
        this.saveProgress();
    }

    nextLevel() {
        this.currentLevelIndex++;
        return this.startLevel();
    }

    restartLevel() {
        return this.startLevel(this.currentLevelIndex);
    }

    getLevelProgress(particleSystem, flowMechanics) {
        if (!this.currentLevel) return 0;
        return this.currentLevel.getProgress(particleSystem, flowMechanics);
    }

    getCompletionStats() {
        if (!this.currentLevel) return null;

        const completionTime = this.currentLevel.startTime ? 
            (Date.now() - this.currentLevel.startTime) / 1000 : 0;

        return {
            levelName: this.currentLevel.name,
            energyReward: this.currentLevel.energyReward,
            bonusReward: this.currentLevel.bonusReward,
            completionTime: Math.round(completionTime),
            stars: this.currentLevel.stars,
            totalEnergy: this.totalEnergy
        };
    }

    // Hedef alanını çizme
    drawTargetAreas(ctx) {
        if (!this.currentLevel) return;

        this.currentLevel.targetConditions.forEach(condition => {
            if (condition.targetArea) {
                this.drawTargetArea(ctx, condition.targetArea, condition);
            } else if (condition.area) {
                this.drawTargetArea(ctx, condition.area, condition);
            }
        });
    }

    drawTargetArea(ctx, area, condition) {
        ctx.save();
        ctx.strokeStyle = '#64ffda80';
        ctx.lineWidth = 2;
        ctx.setLineDash([5, 5]);
        
        // Hedef alan çemberi
        ctx.beginPath();
        ctx.arc(area.x, area.y, area.radius, 0, Math.PI * 2);
        ctx.stroke();
        
        // Merkez noktası
        ctx.fillStyle = '#64ffda';
        ctx.beginPath();
        ctx.arc(area.x, area.y, 3, 0, Math.PI * 2);
        ctx.fill();
        
        // Etiket
        ctx.fillStyle = '#64ffda';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('HEDEF', area.x, area.y - area.radius - 10);
        
        ctx.restore();
    }

    // İlerleme kaydetme
    saveProgress() {
        const progress = {
            currentLevelIndex: this.currentLevelIndex,
            totalEnergy: this.totalEnergy,
            completedLevels: this.completedLevels
        };
        localStorage.setItem('flowCreatorProgress', JSON.stringify(progress));
    }

    // İlerleme yükleme
    loadProgress() {
        const saved = localStorage.getItem('flowCreatorProgress');
        if (saved) {
            const progress = JSON.parse(saved);
            this.currentLevelIndex = progress.currentLevelIndex || 0;
            this.totalEnergy = progress.totalEnergy || 0;
            this.completedLevels = progress.completedLevels || [];
        }
    }

    // İstatistikler
    getStats() {
        return {
            currentLevel: this.currentLevelIndex + 1,
            totalLevels: this.levels.length,
            completedLevels: this.completedLevels.length,
            totalEnergy: this.totalEnergy,
            completionRate: (this.completedLevels.length / this.levels.length) * 100
        };
    }
}
