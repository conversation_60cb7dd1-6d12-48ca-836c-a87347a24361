<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ak<PERSON>ş Yaratıcısı - Flow Creator</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div id="game-container">
        <!-- Ana <PERSON>ı -->
        <div id="main-game" class="screen active">
            <div id="game-header">
                <div id="level-info">
                    <span id="level-number">Deney 1</span>
                    <span id="objective">Tüm partikülleri merkeze topla</span>
                </div>
                <div id="energy-counter">
                    <span id="energy-icon">⚡</span>
                    <span id="energy-amount">0</span>
                </div>
            </div>
            
            <canvas id="game-canvas"></canvas>
            
            <div id="game-controls">
                <button id="pause-btn">⏸️</button>
                <button id="reset-btn">🔄</button>
                <button id="universe-btn">🌌</button>
            </div>
            
            <div id="progress-bar">
                <div id="progress-fill"></div>
            </div>
        </div>

        <!-- Mikro-Evren Ekranı -->
        <div id="micro-universe" class="screen">
            <div id="universe-header">
                <h2>Mikro-Evreniniz</h2>
                <button id="back-to-game">← Deneylere Dön</button>
            </div>
            
            <div id="universe-stats">
                <div class="stat">
                    <span class="stat-label">Toplam Enerji:</span>
                    <span id="total-energy">0</span>
                </div>
                <div class="stat">
                    <span class="stat-label">Tamamlanan Deneyler:</span>
                    <span id="completed-experiments">0</span>
                </div>
                <div class="stat">
                    <span class="stat-label">Akış Komboları:</span>
                    <span id="total-combos">0</span>
                </div>
            </div>
            
            <div id="universe-canvas-container">
                <canvas id="universe-canvas"></canvas>
            </div>
            
            <div id="universe-shop">
                <h3>Yeni Akış Elementleri</h3>
                <div id="shop-items">
                    <!-- Dinamik olarak doldurulacak -->
                </div>
            </div>
        </div>

        <!-- Başlangıç Ekranı -->
        <div id="start-screen" class="screen">
            <div id="title">
                <h1>Akış Yaratıcısı</h1>
                <p>Kozmik partikülleri yönlendir, akışlar yarat, evreni şekillendir</p>
            </div>
            <button id="start-game">Deneylere Başla</button>
            <button id="view-universe">Mikro-Evreni Gör</button>
        </div>

        <!-- Seviye Tamamlama Ekranı -->
        <div id="level-complete" class="screen">
            <div id="completion-content">
                <h2>Deney Tamamlandı!</h2>
                <div id="completion-stats">
                    <div class="completion-stat">
                        <span class="stat-label">Kazanılan Enerji:</span>
                        <span id="earned-energy">0</span>
                    </div>
                    <div class="completion-stat">
                        <span class="stat-label">Akış Komboları:</span>
                        <span id="level-combos">0</span>
                    </div>
                    <div class="completion-stat">
                        <span class="stat-label">Tamamlanma Süresi:</span>
                        <span id="completion-time">0s</span>
                    </div>
                </div>
                <div id="completion-buttons">
                    <button id="next-level">Sonraki Deney</button>
                    <button id="replay-level">Tekrar Oyna</button>
                    <button id="go-to-universe">Mikro-Evren</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Ses Kontrolleri -->
    <div id="audio-controls">
        <button id="sound-toggle">🔊</button>
        <input type="range" id="volume-slider" min="0" max="100" value="50">
    </div>

    <!-- JavaScript Dosyaları -->
    <script src="js/particle-system.js"></script>
    <script src="js/flow-mechanics.js"></script>
    <script src="js/level-manager.js"></script>
    <script src="js/micro-universe.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
