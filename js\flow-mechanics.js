// Akış Yaratıcısı - Akış Mekaniği

class FlowField {
    constructor(x, y, strength, type = 'attract') {
        this.x = x;
        this.y = y;
        this.strength = strength;
        this.type = type; // 'attract', 'repel', 'vortex'
        this.radius = 100;
        this.life = 1.0;
        this.maxLife = 1.0;
        this.visualRadius = 0;
        this.pulsePhase = 0;
    }

    update(deltaTime) {
        this.life -= deltaTime * 0.5; // 2 saniye yaşam süresi
        this.pulsePhase += deltaTime * 3;
        this.visualRadius = this.radius * (1 - this.life) * 0.5;
    }

    draw(ctx) {
        if (this.life <= 0) return;

        ctx.save();
        ctx.globalAlpha = this.life * 0.3;
        
        // Dış halka
        const gradient = ctx.createRadialGradient(
            this.x, this.y, 0,
            this.x, this.y, this.radius
        );
        
        if (this.type === 'attract') {
            gradient.addColorStop(0, '#64ffda40');
            gradient.addColorStop(1, '#64ffda00');
        } else if (this.type === 'repel') {
            gradient.addColorStop(0, '#ff644040');
            gradient.addColorStop(1, '#ff644000');
        } else if (this.type === 'vortex') {
            gradient.addColorStop(0, '#9c27b040');
            gradient.addColorStop(1, '#9c27b000');
        }
        
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
        ctx.fill();
        
        // Merkez nokta
        const pulseSize = 5 + Math.sin(this.pulsePhase) * 2;
        ctx.globalAlpha = this.life;
        ctx.fillStyle = this.type === 'attract' ? '#64ffda' : 
                       this.type === 'repel' ? '#ff6440' : '#9c27b0';
        ctx.beginPath();
        ctx.arc(this.x, this.y, pulseSize, 0, Math.PI * 2);
        ctx.fill();
        
        ctx.restore();
    }

    isAlive() {
        return this.life > 0;
    }
}

class FlowPath {
    constructor() {
        this.points = [];
        this.strength = 1.0;
        this.width = 5;
        this.life = 2.0;
        this.maxLife = 2.0;
        this.color = '#64ffda';
        this.particlesAffected = new Set();
    }

    addPoint(x, y) {
        this.points.push({ x, y, life: this.life });
    }

    update(deltaTime) {
        this.life -= deltaTime * 0.3;
        
        // Nokta yaşam sürelerini güncelle
        this.points.forEach(point => {
            point.life -= deltaTime * 0.5;
        });
        
        // Ölü noktaları kaldır
        this.points = this.points.filter(point => point.life > 0);
    }

    draw(ctx) {
        if (this.points.length < 2) return;

        ctx.save();
        ctx.strokeStyle = this.color;
        ctx.lineWidth = this.width * (this.life / this.maxLife);
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';
        ctx.globalAlpha = this.life / this.maxLife;
        
        // Parıltı efekti
        ctx.shadowColor = this.color;
        ctx.shadowBlur = 10;
        
        ctx.beginPath();
        ctx.moveTo(this.points[0].x, this.points[0].y);
        
        for (let i = 1; i < this.points.length; i++) {
            const alpha = this.points[i].life / this.maxLife;
            ctx.globalAlpha = alpha * (this.life / this.maxLife);
            ctx.lineTo(this.points[i].x, this.points[i].y);
        }
        
        ctx.stroke();
        ctx.restore();
    }

    isAlive() {
        return this.life > 0 && this.points.length > 0;
    }

    // Yola yakın partikülleri etkileme
    affectParticles(particles) {
        if (this.points.length < 2) return;

        particles.forEach(particle => {
            let minDistance = Infinity;
            let closestPoint = null;
            let segmentIndex = -1;

            // En yakın yol segmentini bul
            for (let i = 0; i < this.points.length - 1; i++) {
                const p1 = this.points[i];
                const p2 = this.points[i + 1];
                const distance = this.distanceToLineSegment(particle.x, particle.y, p1, p2);
                
                if (distance < minDistance) {
                    minDistance = distance;
                    closestPoint = this.getClosestPointOnSegment(particle.x, particle.y, p1, p2);
                    segmentIndex = i;
                }
            }

            // Etki alanı içindeyse partiküle kuvvet uygula
            if (minDistance < 50 && closestPoint) {
                const force = (50 - minDistance) / 50 * this.strength * 0.1;
                const dx = closestPoint.x - particle.x;
                const dy = closestPoint.y - particle.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                
                if (distance > 0) {
                    particle.vx += (dx / distance) * force;
                    particle.vy += (dy / distance) * force;
                    particle.glowIntensity = Math.max(particle.glowIntensity, force * 2);
                    
                    this.particlesAffected.add(particle);
                }
            }
        });
    }

    distanceToLineSegment(px, py, p1, p2) {
        const A = px - p1.x;
        const B = py - p1.y;
        const C = p2.x - p1.x;
        const D = p2.y - p1.y;

        const dot = A * C + B * D;
        const lenSq = C * C + D * D;
        
        if (lenSq === 0) {
            return Math.sqrt(A * A + B * B);
        }
        
        let param = dot / lenSq;
        param = Math.max(0, Math.min(1, param));
        
        const xx = p1.x + param * C;
        const yy = p1.y + param * D;
        
        const dx = px - xx;
        const dy = py - yy;
        
        return Math.sqrt(dx * dx + dy * dy);
    }

    getClosestPointOnSegment(px, py, p1, p2) {
        const A = px - p1.x;
        const B = py - p1.y;
        const C = p2.x - p1.x;
        const D = p2.y - p1.y;

        const dot = A * C + B * D;
        const lenSq = C * C + D * D;
        
        if (lenSq === 0) {
            return { x: p1.x, y: p1.y };
        }
        
        let param = dot / lenSq;
        param = Math.max(0, Math.min(1, param));
        
        return {
            x: p1.x + param * C,
            y: p1.y + param * D
        };
    }
}

class FlowMechanics {
    constructor(particleSystem) {
        this.particleSystem = particleSystem;
        this.flowFields = [];
        this.flowPaths = [];
        this.currentPath = null;
        this.isDrawing = false;
        this.comboCount = 0;
        this.lastComboTime = 0;
        this.comboMultiplier = 1;
        this.totalCombos = 0;
    }

    // Tek dokunuş - manyetik alan oluşturma
    createFlowField(x, y, type = 'attract', strength = 1.0) {
        const field = new FlowField(x, y, strength, type);
        this.flowFields.push(field);
        
        // Ses efekti tetikleme (gelecekte eklenecek)
        this.triggerSoundEffect('field_create');
        
        return field;
    }

    // Yol çizmeye başlama
    startDrawingPath(x, y) {
        this.currentPath = new FlowPath();
        this.currentPath.addPoint(x, y);
        this.isDrawing = true;
    }

    // Yol çizmeye devam etme
    continueDrawingPath(x, y) {
        if (this.isDrawing && this.currentPath) {
            this.currentPath.addPoint(x, y);
        }
    }

    // Yol çizmeyi bitirme
    finishDrawingPath() {
        if (this.currentPath && this.currentPath.points.length > 1) {
            this.flowPaths.push(this.currentPath);
            this.checkForCombo();
        }
        this.currentPath = null;
        this.isDrawing = false;
    }

    // Kombo kontrolü
    checkForCombo() {
        const currentTime = Date.now();
        const timeSinceLastCombo = currentTime - this.lastComboTime;
        
        if (timeSinceLastCombo < 3000) { // 3 saniye içinde
            this.comboCount++;
            this.comboMultiplier = Math.min(5, 1 + this.comboCount * 0.5);
        } else {
            this.comboCount = 1;
            this.comboMultiplier = 1;
        }
        
        this.lastComboTime = currentTime;
        this.totalCombos++;
        
        // Kombo efekti
        if (this.comboCount > 1) {
            this.triggerComboEffect();
        }
    }

    // Kombo efekti
    triggerComboEffect() {
        // Tüm partiküllere geçici parıltı efekti
        this.particleSystem.particles.forEach(particle => {
            particle.glowIntensity = Math.max(particle.glowIntensity, this.comboMultiplier);
        });
        
        // Ses efekti
        this.triggerSoundEffect('combo', this.comboCount);
    }

    update(deltaTime) {
        // Akış alanlarını güncelle
        this.flowFields = this.flowFields.filter(field => {
            field.update(deltaTime);
            
            if (field.isAlive()) {
                // Yakındaki partiküllere kuvvet uygula
                this.particleSystem.particles.forEach(particle => {
                    const dx = field.x - particle.x;
                    const dy = field.y - particle.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    
                    if (distance < field.radius) {
                        if (field.type === 'attract') {
                            particle.applyMagneticForce(field.x, field.y, field.strength, true);
                        } else if (field.type === 'repel') {
                            particle.applyMagneticForce(field.x, field.y, field.strength, false);
                        } else if (field.type === 'vortex') {
                            // Dönel kuvvet
                            const angle = Math.atan2(dy, dx) + Math.PI / 2;
                            const force = field.strength * 0.1;
                            particle.vx += Math.cos(angle) * force;
                            particle.vy += Math.sin(angle) * force;
                        }
                    }
                });
                return true;
            }
            return false;
        });

        // Akış yollarını güncelle
        this.flowPaths = this.flowPaths.filter(path => {
            path.update(deltaTime);
            
            if (path.isAlive()) {
                path.affectParticles(this.particleSystem.particles);
                return true;
            }
            return false;
        });

        // Kombo zamanlayıcısı
        const currentTime = Date.now();
        if (currentTime - this.lastComboTime > 5000) {
            this.comboCount = 0;
            this.comboMultiplier = 1;
        }
    }

    draw(ctx) {
        // Akış alanlarını çiz
        this.flowFields.forEach(field => {
            field.draw(ctx);
        });

        // Akış yollarını çiz
        this.flowPaths.forEach(path => {
            path.draw(ctx);
        });

        // Çizilmekte olan yolu çiz
        if (this.currentPath) {
            this.currentPath.draw(ctx);
        }

        // Kombo göstergesi
        if (this.comboCount > 1) {
            this.drawComboIndicator(ctx);
        }
    }

    drawComboIndicator(ctx) {
        ctx.save();
        ctx.font = 'bold 24px Arial';
        ctx.fillStyle = '#64ffda';
        ctx.textAlign = 'center';
        ctx.shadowColor = '#64ffda';
        ctx.shadowBlur = 10;
        
        const text = `KOMBO x${this.comboCount}`;
        const x = ctx.canvas.width / 2;
        const y = 50;
        
        // Animasyonlu boyut
        const scale = 1 + Math.sin(Date.now() * 0.01) * 0.1;
        ctx.scale(scale, scale);
        
        ctx.fillText(text, x / scale, y / scale);
        ctx.restore();
    }

    // Renk dönüştürme alanı oluşturma
    createColorTransformer(x, y, fromColor, toColor) {
        const field = new FlowField(x, y, 1.0, 'transform');
        field.fromColor = fromColor;
        field.toColor = toColor;
        field.radius = 80;
        
        // Yakındaki partiküllerin rengini değiştir
        this.particleSystem.particles.forEach(particle => {
            const dx = field.x - particle.x;
            const dy = field.y - particle.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            
            if (distance < field.radius && particle.color === fromColor) {
                particle.changeColor(toColor);
            }
        });
        
        this.flowFields.push(field);
    }

    // Ses efekti tetikleme (placeholder)
    triggerSoundEffect(type, intensity = 1) {
        // Gelecekte ses sistemi eklendiğinde kullanılacak
        console.log(`Sound effect: ${type}, intensity: ${intensity}`);
    }

    // İstatistikleri al
    getStats() {
        return {
            totalCombos: this.totalCombos,
            currentCombo: this.comboCount,
            comboMultiplier: this.comboMultiplier,
            activeFields: this.flowFields.length,
            activePaths: this.flowPaths.length
        };
    }

    // Temizleme
    clear() {
        this.flowFields = [];
        this.flowPaths = [];
        this.currentPath = null;
        this.isDrawing = false;
        this.comboCount = 0;
        this.comboMultiplier = 1;
    }
}
