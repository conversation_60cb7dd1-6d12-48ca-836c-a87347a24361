// <PERSON>k<PERSON>ş Yaratıcısı - Ana Oyun Dosyası

class FlowCreatorGame {
    constructor() {
        this.canvas = document.getElementById('game-canvas');
        this.ctx = this.canvas.getContext('2d');
        this.universeCanvas = document.getElementById('universe-canvas');

        // Oyun sistemleri
        this.particleSystem = null;
        this.flowMechanics = null;
        this.levelManager = null;
        this.microUniverse = null;

        // Oyun durumu
        this.gameState = 'start'; // 'start', 'playing', 'paused', 'universe', 'levelComplete'
        this.lastTime = 0;
        this.isRunning = false;

        // Giriş kontrolü
        this.input = {
            isMouseDown: false,
            mouseX: 0,
            mouseY: 0,
            lastMouseX: 0,
            lastMouseY: 0,
            touchId: null
        };

        // Ses kontrolü
        this.soundEnabled = true;
        this.volume = 0.5;

        this.initialize();
    }

    initialize() {
        this.setupCanvas();
        this.setupEventListeners();
        this.initializeSystems();
        this.showStartScreen();
    }

    setupCanvas() {
        // Ana oyun canvas'ı
        this.resizeCanvas();
        window.addEventListener('resize', () => this.resizeCanvas());

        // Mikro-evren canvas'ı
        if (this.universeCanvas) {
            this.universeCanvas.width = 800;
            this.universeCanvas.height = 600;
        }
    }

    resizeCanvas() {
        const container = this.canvas.parentElement;
        const rect = container.getBoundingClientRect();

        this.canvas.width = rect.width - 40; // Padding için
        this.canvas.height = rect.height - 40;

        // Canvas boyutu değiştiğinde sistemleri güncelle
        if (this.particleSystem) {
            this.particleSystem.canvas = this.canvas;
        }
    }

    setupEventListeners() {
        // Mouse olayları
        this.canvas.addEventListener('mousedown', (e) => this.handleMouseDown(e));
        this.canvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        this.canvas.addEventListener('mouseup', (e) => this.handleMouseUp(e));

        // Touch olayları
        this.canvas.addEventListener('touchstart', (e) => this.handleTouchStart(e));
        this.canvas.addEventListener('touchmove', (e) => this.handleTouchMove(e));
        this.canvas.addEventListener('touchend', (e) => this.handleTouchEnd(e));

        // Mikro-evren olayları
        if (this.universeCanvas) {
            this.universeCanvas.addEventListener('click', (e) => this.handleUniverseClick(e));
        }

        // UI buton olayları
        this.setupUIEventListeners();
    }

    setupUIEventListeners() {
        // Oyun kontrolleri
        document.getElementById('start-game')?.addEventListener('click', () => this.startGame());
        document.getElementById('view-universe')?.addEventListener('click', () => this.showUniverse());
        document.getElementById('pause-btn')?.addEventListener('click', () => this.togglePause());
        document.getElementById('reset-btn')?.addEventListener('click', () => this.resetLevel());
        document.getElementById('universe-btn')?.addEventListener('click', () => this.showUniverse());

        // Seviye tamamlama
        document.getElementById('next-level')?.addEventListener('click', () => this.nextLevel());
        document.getElementById('replay-level')?.addEventListener('click', () => this.replayLevel());
        document.getElementById('go-to-universe')?.addEventListener('click', () => this.showUniverse());

        // Mikro-evren
        document.getElementById('back-to-game')?.addEventListener('click', () => this.backToGame());

        // Ses kontrolleri
        document.getElementById('sound-toggle')?.addEventListener('click', () => this.toggleSound());
        document.getElementById('volume-slider')?.addEventListener('input', (e) => {
            this.volume = e.target.value / 100;
        });
    }

    initializeSystems() {
        // Partikül sistemi
        this.particleSystem = new ParticleSystem(this.canvas);

        // Akış mekaniği
        this.flowMechanics = new FlowMechanics(this.particleSystem);

        // Seviye yöneticisi
        this.levelManager = new LevelManager();
        this.levelManager.loadProgress();

        // Mikro-evren
        this.microUniverse = new MicroUniverse(this.universeCanvas);
        this.microUniverse.loadUniverse();
    }

    // Oyun döngüsü
    gameLoop(currentTime) {
        if (!this.isRunning) return;

        const deltaTime = (currentTime - this.lastTime) / 1000;
        this.lastTime = currentTime;

        this.update(deltaTime);
        this.render();

        requestAnimationFrame((time) => this.gameLoop(time));
    }

    update(deltaTime) {
        switch (this.gameState) {
            case 'playing':
                this.updateGameplay(deltaTime);
                break;
            case 'universe':
                this.updateUniverse(deltaTime);
                break;
        }
    }

    updateGameplay(deltaTime) {
        // Sistemleri güncelle
        this.particleSystem.update(deltaTime);
        this.flowMechanics.update(deltaTime);

        // Seviye ilerlemesini kontrol et
        if (this.levelManager.checkLevelCompletion(this.particleSystem, this.flowMechanics)) {
            this.completeLevel();
        }

        // UI güncellemeleri
        this.updateUI();
    }

    updateUniverse(deltaTime) {
        this.microUniverse.update(deltaTime);
        this.updateUniverseUI();
    }

    render() {
        switch (this.gameState) {
            case 'playing':
                this.renderGameplay();
                break;
            case 'universe':
                this.renderUniverse();
                break;
        }
    }

    renderGameplay() {
        // Partikül sistemi çizimi
        this.particleSystem.draw();

        // Akış mekaniği çizimi
        this.flowMechanics.draw(this.ctx);

        // Seviye hedeflerini çiz
        this.levelManager.drawTargetAreas(this.ctx);
    }

    renderUniverse() {
        this.microUniverse.draw();
    }

    // Giriş işleme
    handleMouseDown(e) {
        if (this.gameState !== 'playing') return;

        const rect = this.canvas.getBoundingClientRect();
        this.input.mouseX = e.clientX - rect.left;
        this.input.mouseY = e.clientY - rect.top;
        this.input.lastMouseX = this.input.mouseX;
        this.input.lastMouseY = this.input.mouseY;
        this.input.isMouseDown = true;

        // Tek dokunuş - manyetik alan
        this.flowMechanics.createFlowField(this.input.mouseX, this.input.mouseY, 'attract', 1.0);
    }

    handleMouseMove(e) {
        if (this.gameState !== 'playing') return;

        const rect = this.canvas.getBoundingClientRect();
        this.input.mouseX = e.clientX - rect.left;
        this.input.mouseY = e.clientY - rect.top;

        if (this.input.isMouseDown) {
            // Sürükleme - akış yolu oluşturma
            if (!this.flowMechanics.isDrawing) {
                this.flowMechanics.startDrawingPath(this.input.lastMouseX, this.input.lastMouseY);
            }
            this.flowMechanics.continueDrawingPath(this.input.mouseX, this.input.mouseY);
        }

        this.input.lastMouseX = this.input.mouseX;
        this.input.lastMouseY = this.input.mouseY;
    }

    handleMouseUp(e) {
        if (this.gameState !== 'playing') return;

        this.input.isMouseDown = false;

        if (this.flowMechanics.isDrawing) {
            this.flowMechanics.finishDrawingPath();
        }
    }

    // Touch olayları (mobil destek)
    handleTouchStart(e) {
        e.preventDefault();
        if (e.touches.length === 1) {
            const touch = e.touches[0];
            const mouseEvent = new MouseEvent('mousedown', {
                clientX: touch.clientX,
                clientY: touch.clientY
            });
            this.handleMouseDown(mouseEvent);
            this.input.touchId = touch.identifier;
        }
    }

    handleTouchMove(e) {
        e.preventDefault();
        if (e.touches.length === 1 && this.input.touchId !== null) {
            const touch = Array.from(e.touches).find(t => t.identifier === this.input.touchId);
            if (touch) {
                const mouseEvent = new MouseEvent('mousemove', {
                    clientX: touch.clientX,
                    clientY: touch.clientY
                });
                this.handleMouseMove(mouseEvent);
            }
        }
    }

    handleTouchEnd(e) {
        e.preventDefault();
        this.input.touchId = null;
        const mouseEvent = new MouseEvent('mouseup', {});
        this.handleMouseUp(mouseEvent);
    }

    // Mikro-evren tıklama
    handleUniverseClick(e) {
        if (this.gameState !== 'universe') return;

        const rect = this.universeCanvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        const clickedStructure = this.microUniverse.handleClick(x, y);
        if (clickedStructure) {
            this.showStructureInfo(clickedStructure);
        }
    }

    // Oyun durumu yönetimi
    startGame() {
        this.gameState = 'playing';
        this.showScreen('main-game');

        // İlk seviyeyi başlat
        const level = this.levelManager.startLevel();
        if (level) {
            this.updateLevelInfo(level);
        }

        // Oyun döngüsünü başlat
        this.isRunning = true;
        this.lastTime = performance.now();
        requestAnimationFrame((time) => this.gameLoop(time));
    }

    showUniverse() {
        this.gameState = 'universe';
        this.showScreen('micro-universe');

        // Mikro-evren verilerini güncelle
        this.microUniverse.totalEnergy = this.levelManager.totalEnergy;
        this.microUniverse.unlockShopItems();
        this.updateUniverseUI();

        if (!this.isRunning) {
            this.isRunning = true;
            this.lastTime = performance.now();
            requestAnimationFrame((time) => this.gameLoop(time));
        }
    }

    backToGame() {
        this.gameState = 'playing';
        this.showScreen('main-game');

        // Mikro-evren enerjisini seviye yöneticisine aktar
        this.levelManager.totalEnergy = this.microUniverse.totalEnergy;
    }

    showStartScreen() {
        this.gameState = 'start';
        this.showScreen('start-screen');
        this.isRunning = false;
    }

    completeLevel() {
        this.gameState = 'levelComplete';
        this.showScreen('level-complete');

        const stats = this.levelManager.getCompletionStats();
        this.updateCompletionScreen(stats);

        // Mikro-evren enerjisini güncelle
        this.microUniverse.totalEnergy = this.levelManager.totalEnergy;
        this.microUniverse.saveUniverse();
    }

    nextLevel() {
        const nextLevel = this.levelManager.nextLevel();
        if (nextLevel) {
            this.gameState = 'playing';
            this.showScreen('main-game');
            this.updateLevelInfo(nextLevel);
            this.resetGameState();
        } else {
            // Tüm seviyeler tamamlandı
            this.showUniverse();
        }
    }

    replayLevel() {
        const level = this.levelManager.restartLevel();
        this.gameState = 'playing';
        this.showScreen('main-game');
        this.updateLevelInfo(level);
        this.resetGameState();
    }

    resetLevel() {
        const level = this.levelManager.restartLevel();
        this.updateLevelInfo(level);
        this.resetGameState();
    }

    resetGameState() {
        // Sistemleri temizle
        this.particleSystem.clear();
        this.flowMechanics.clear();

        // Yeni partiküller ekle
        for (let i = 0; i < 15; i++) {
            const x = Math.random() * this.canvas.width;
            const y = Math.random() * this.canvas.height;
            const colors = ['#64ffda', '#00bcd4', '#3f51b5'];
            const color = colors[Math.floor(Math.random() * colors.length)];
            this.particleSystem.addParticle(x, y, color);
        }
    }

    togglePause() {
        if (this.gameState === 'playing') {
            this.gameState = 'paused';
            this.isRunning = false;
            document.getElementById('pause-btn').textContent = '▶️';
        } else if (this.gameState === 'paused') {
            this.gameState = 'playing';
            this.isRunning = true;
            this.lastTime = performance.now();
            requestAnimationFrame((time) => this.gameLoop(time));
            document.getElementById('pause-btn').textContent = '⏸️';
        }
    }

    toggleSound() {
        this.soundEnabled = !this.soundEnabled;
        const button = document.getElementById('sound-toggle');
        button.textContent = this.soundEnabled ? '🔊' : '🔇';
    }

    // UI güncellemeleri
    updateUI() {
        // Enerji sayacı
        const energyElement = document.getElementById('energy-amount');
        if (energyElement) {
            energyElement.textContent = this.levelManager.totalEnergy;
        }

        // İlerleme çubuğu
        const progress = this.levelManager.getLevelProgress(this.particleSystem, this.flowMechanics);
        const progressFill = document.getElementById('progress-fill');
        if (progressFill) {
            progressFill.style.width = `${progress * 100}%`;
        }

        // Kombo göstergesi
        const stats = this.flowMechanics.getStats();
        if (stats.currentCombo > 1) {
            // Kombo animasyonu (CSS ile hallediliyor)
        }
    }

    updateLevelInfo(level) {
        const levelNumber = document.getElementById('level-number');
        const objective = document.getElementById('objective');

        if (levelNumber) levelNumber.textContent = level.name;
        if (objective) objective.textContent = level.objective;
    }

    updateCompletionScreen(stats) {
        document.getElementById('earned-energy').textContent = stats.energyReward;
        document.getElementById('level-combos').textContent = this.flowMechanics.getStats().totalCombos;
        document.getElementById('completion-time').textContent = `${stats.completionTime}s`;

        // Yıldız gösterimi
        const starsContainer = document.querySelector('.completion-stars');
        if (starsContainer) {
            starsContainer.innerHTML = '★'.repeat(stats.stars) + '☆'.repeat(3 - stats.stars);
        }
    }

    updateUniverseUI() {
        const stats = this.microUniverse.getStats();

        document.getElementById('total-energy').textContent = stats.totalEnergy;
        document.getElementById('completed-experiments').textContent = this.levelManager.completedLevels.length;
        document.getElementById('total-combos').textContent = this.flowMechanics.getStats().totalCombos;

        // Mağaza öğelerini güncelle
        this.updateShopUI();
    }

    updateShopUI() {
        const shopContainer = document.getElementById('shop-items');
        if (!shopContainer) return;

        shopContainer.innerHTML = '';

        this.microUniverse.shopItems.forEach(item => {
            const itemElement = document.createElement('div');
            itemElement.className = `shop-item ${item.unlocked ? 'unlocked' : 'locked'}`;
            itemElement.innerHTML = `
                <h4>${item.name}</h4>
                <p>${item.description}</p>
                <div class="cost">💎 ${item.cost}</div>
                <button ${item.unlocked && this.microUniverse.totalEnergy >= item.cost ? '' : 'disabled'}>
                    Satın Al
                </button>
            `;

            const button = itemElement.querySelector('button');
            button.addEventListener('click', () => {
                if (this.microUniverse.purchaseItem(item.id)) {
                    this.updateUniverseUI();
                }
            });

            shopContainer.appendChild(itemElement);
        });
    }

    showStructureInfo(structure) {
        // Yapı bilgilerini göster (modal veya side panel)
        console.log('Selected structure:', structure);
    }

    // Yardımcı fonksiyonlar
    showScreen(screenId) {
        // Tüm ekranları gizle
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.remove('active');
        });

        // Belirtilen ekranı göster
        const targetScreen = document.getElementById(screenId);
        if (targetScreen) {
            targetScreen.classList.add('active');
        }
    }
}

// Oyunu başlat
document.addEventListener('DOMContentLoaded', () => {
    window.flowCreatorGame = new FlowCreatorGame();
});
