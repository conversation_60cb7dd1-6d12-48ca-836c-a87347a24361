// Akış Yaratıcısı - Partikül Sistemi

class Particle {
    constructor(x, y, color = '#64ffda', size = 3) {
        this.x = x;
        this.y = y;
        this.vx = (Math.random() - 0.5) * 2; // Başlangıç hızı
        this.vy = (Math.random() - 0.5) * 2;
        this.color = color;
        this.size = size;
        this.originalSize = size;
        this.life = 1.0; // Yaşam süresi (0-1)
        this.maxLife = 1.0;
        this.trail = []; // İz bırakma için
        this.energy = 1.0; // Enerji seviyesi
        this.magneticInfluence = 0; // Manyetik alan etkisi
        this.targetX = null; // Hedef koordinatları
        this.targetY = null;
        this.isSelected = false; // Seçili durumu
        this.glowIntensity = 0; // Parıltı yoğunluğu
    }

    update(deltaTime, canvasWidth, canvasHeight) {
        // Manyetik alan et<PERSON> azalt
        this.magneticInfluence *= 0.95;
        
        // Hedef varsa ona doğru hareket et
        if (this.targetX !== null && this.targetY !== null) {
            const dx = this.targetX - this.x;
            const dy = this.targetY - this.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            
            if (distance > 5) {
                this.vx += (dx / distance) * 0.1;
                this.vy += (dy / distance) * 0.1;
            } else {
                this.targetX = null;
                this.targetY = null;
            }
        }
        
        // Hız sınırlaması
        const maxSpeed = 5;
        const speed = Math.sqrt(this.vx * this.vx + this.vy * this.vy);
        if (speed > maxSpeed) {
            this.vx = (this.vx / speed) * maxSpeed;
            this.vy = (this.vy / speed) * maxSpeed;
        }
        
        // Pozisyon güncelleme
        this.x += this.vx * deltaTime;
        this.y += this.vy * deltaTime;
        
        // Sınır kontrolü - elastik çarpışma
        if (this.x <= this.size || this.x >= canvasWidth - this.size) {
            this.vx *= -0.8; // Enerji kaybı ile geri sekme
            this.x = Math.max(this.size, Math.min(canvasWidth - this.size, this.x));
        }
        if (this.y <= this.size || this.y >= canvasHeight - this.size) {
            this.vy *= -0.8;
            this.y = Math.max(this.size, Math.min(canvasHeight - this.size, this.y));
        }
        
        // İz güncelleme
        this.trail.push({ x: this.x, y: this.y, life: 1.0 });
        if (this.trail.length > 10) {
            this.trail.shift();
        }
        
        // İz yaşam süresi azaltma
        this.trail.forEach(point => {
            point.life -= deltaTime * 2;
        });
        this.trail = this.trail.filter(point => point.life > 0);
        
        // Parıltı efekti güncelleme
        this.glowIntensity = Math.max(0, this.glowIntensity - deltaTime);
        
        // Boyut animasyonu
        if (this.isSelected) {
            this.size = this.originalSize * 1.5;
        } else {
            this.size = this.originalSize;
        }
    }

    draw(ctx) {
        // İz çizimi
        this.trail.forEach((point, index) => {
            const alpha = point.life * 0.3;
            const size = this.size * point.life * 0.5;
            
            ctx.save();
            ctx.globalAlpha = alpha;
            ctx.fillStyle = this.color;
            ctx.beginPath();
            ctx.arc(point.x, point.y, size, 0, Math.PI * 2);
            ctx.fill();
            ctx.restore();
        });
        
        // Ana partikül çizimi
        ctx.save();
        
        // Parıltı efekti
        if (this.glowIntensity > 0 || this.isSelected) {
            const glowSize = this.size * (2 + this.glowIntensity);
            const gradient = ctx.createRadialGradient(
                this.x, this.y, 0,
                this.x, this.y, glowSize
            );
            gradient.addColorStop(0, this.color + '80');
            gradient.addColorStop(1, this.color + '00');
            
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(this.x, this.y, glowSize, 0, Math.PI * 2);
            ctx.fill();
        }
        
        // Ana partikül
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fill();
        
        // Enerji göstergesi (iç halka)
        if (this.energy < 1.0) {
            ctx.strokeStyle = this.color + '80';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.arc(this.x, this.y, this.size * 0.7, 0, Math.PI * 2 * this.energy);
            ctx.stroke();
        }
        
        ctx.restore();
    }

    // Manyetik alan etkisi uygula
    applyMagneticForce(x, y, strength, isAttraction = true) {
        const dx = x - this.x;
        const dy = y - this.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance > 0 && distance < 150) { // Etki alanı
            const force = strength / (distance * distance + 1);
            const forceX = (dx / distance) * force;
            const forceY = (dy / distance) * force;
            
            if (isAttraction) {
                this.vx += forceX;
                this.vy += forceY;
            } else {
                this.vx -= forceX;
                this.vy -= forceY;
            }
            
            this.magneticInfluence = Math.min(1.0, this.magneticInfluence + force);
            this.glowIntensity = Math.min(2.0, this.glowIntensity + force);
        }
    }

    // Renk değiştirme
    changeColor(newColor) {
        this.color = newColor;
        this.glowIntensity = 1.0;
    }

    // Hedef belirleme
    setTarget(x, y) {
        this.targetX = x;
        this.targetY = y;
        this.glowIntensity = 1.5;
    }

    // Partikül seçimi
    select() {
        this.isSelected = true;
        this.glowIntensity = 2.0;
    }

    deselect() {
        this.isSelected = false;
    }

    // Çarpışma kontrolü
    isCollidingWith(other) {
        const dx = this.x - other.x;
        const dy = this.y - other.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        return distance < (this.size + other.size);
    }

    // Nokta ile çarpışma kontrolü
    isPointInside(x, y) {
        const dx = this.x - x;
        const dy = this.y - y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        return distance <= this.size;
    }
}

class ParticleSystem {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.particles = [];
        this.maxParticles = 50;
        this.spawnRate = 0.5; // Saniyede kaç partikül
        this.spawnTimer = 0;
        this.colors = ['#64ffda', '#00bcd4', '#3f51b5', '#9c27b0', '#e91e63'];
        this.currentColorIndex = 0;
    }

    addParticle(x, y, color) {
        if (this.particles.length < this.maxParticles) {
            const particle = new Particle(x, y, color);
            this.particles.push(particle);
            return particle;
        }
        return null;
    }

    removeParticle(particle) {
        const index = this.particles.indexOf(particle);
        if (index > -1) {
            this.particles.splice(index, 1);
        }
    }

    spawnParticles(deltaTime) {
        this.spawnTimer += deltaTime;
        
        if (this.spawnTimer >= this.spawnRate) {
            const spawnX = Math.random() * this.canvas.width;
            const spawnY = Math.random() * this.canvas.height;
            const color = this.colors[this.currentColorIndex % this.colors.length];
            
            this.addParticle(spawnX, spawnY, color);
            this.spawnTimer = 0;
            this.currentColorIndex++;
        }
    }

    update(deltaTime) {
        // Otomatik partikül üretimi
        this.spawnParticles(deltaTime);
        
        // Tüm partikülleri güncelle
        this.particles.forEach(particle => {
            particle.update(deltaTime, this.canvas.width, this.canvas.height);
        });
        
        // Çarpışma kontrolü
        for (let i = 0; i < this.particles.length; i++) {
            for (let j = i + 1; j < this.particles.length; j++) {
                if (this.particles[i].isCollidingWith(this.particles[j])) {
                    this.handleCollision(this.particles[i], this.particles[j]);
                }
            }
        }
    }

    handleCollision(particle1, particle2) {
        // Elastik çarpışma
        const dx = particle2.x - particle1.x;
        const dy = particle2.y - particle1.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance > 0) {
            const nx = dx / distance;
            const ny = dy / distance;
            
            const relativeVelocityX = particle2.vx - particle1.vx;
            const relativeVelocityY = particle2.vy - particle1.vy;
            
            const speed = relativeVelocityX * nx + relativeVelocityY * ny;
            
            if (speed < 0) return; // Zaten ayrılıyorlar
            
            particle1.vx += speed * nx * 0.5;
            particle1.vy += speed * ny * 0.5;
            particle2.vx -= speed * nx * 0.5;
            particle2.vy -= speed * ny * 0.5;
            
            // Parıltı efekti
            particle1.glowIntensity = 1.0;
            particle2.glowIntensity = 1.0;
        }
    }

    draw() {
        // Canvas temizleme
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Tüm partikülleri çiz
        this.particles.forEach(particle => {
            particle.draw(this.ctx);
        });
    }

    // Belirli bir alandaki partikülleri al
    getParticlesInArea(x, y, radius) {
        return this.particles.filter(particle => {
            const dx = particle.x - x;
            const dy = particle.y - y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            return distance <= radius;
        });
    }

    // Belirli renkteki partikülleri al
    getParticlesByColor(color) {
        return this.particles.filter(particle => particle.color === color);
    }

    // Tüm partikülleri temizle
    clear() {
        this.particles = [];
    }

    // Partikül sayısını al
    getParticleCount() {
        return this.particles.length;
    }
}
